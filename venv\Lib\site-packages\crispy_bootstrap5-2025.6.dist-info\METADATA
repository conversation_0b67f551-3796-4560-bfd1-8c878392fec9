Metadata-Version: 2.4
Name: crispy-bootstrap5
Version: 2025.6
Summary: Bootstrap5 template pack for django-crispy-forms
Author: <PERSON>
License: MIT
Project-URL: CI, https://github.com/django-crispy-forms/crispy-bootstrap5/actions
Project-URL: Changelog, https://github.com/django-crispy-forms/crispy-bootstrap5/releases
Project-URL: Homepage, https://github.com/django-crispy-forms/crispy-bootstrap5
Project-URL: Issues, https://github.com/django-crispy-forms/crispy-bootstrap5/issues
Classifier: Environment :: Web Environment
Classifier: Framework :: Django
Classifier: Framework :: Django :: 4.2
Classifier: Framework :: Django :: 5.0
Classifier: Framework :: Django :: 5.1
Classifier: Framework :: Django :: 5.2
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Internet :: WWW/HTTP
Classifier: Topic :: Internet :: WWW/HTTP :: Dynamic Content
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: django>=4.2
Requires-Dist: django-crispy-forms>=2.3
Provides-Extra: test
Requires-Dist: pytest; extra == "test"
Requires-Dist: pytest-django; extra == "test"
Dynamic: license-file

# crispy-bootstrap5

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](https://github.com/smithdc1/crispy-bootstrap5/blob/main/LICENSE)

Bootstrap5 template pack for django-crispy-forms

## Installation

Install this plugin using `pip`:
```bash
$ pip install crispy-bootstrap5
```

## Usage

You will need to update your project's settings file to add ``crispy_forms``
and ``crispy_bootstrap5`` to your projects ``INSTALLED_APPS``. Also set
``bootstrap5`` as and allowed template pack and as the default template pack
for your project

```python
INSTALLED_APPS = (
    ...
    "crispy_forms",
    "crispy_bootstrap5",
    ...
)

CRISPY_ALLOWED_TEMPLATE_PACKS = "bootstrap5"

CRISPY_TEMPLATE_PACK = "bootstrap5"
```

## What's new?

Bootstrap 5 introduces [floating labels](https://getbootstrap.com/docs/5.0/forms/floating-labels/).
This template pack include a layout object to use this input type

```python
from crispy_bootstrap5.bootstrap5 import FloatingField

# then in your Layout
... Layout(
    FloatingField("first_name"),
)
```

Accordions also have new features, such as [Accordion flush](https://getbootstrap.com/docs/5.0/components/accordion/#flush) and [Always open](https://getbootstrap.com/docs/5.0/components/accordion/#always-open).
There is a new layout object to use them

```python
from crispy_bootstrap5.bootstrap5 import BS5Accordion

# then in your Layout
# if not informed, flush and always_open default to False
... Layout(
    BS5Accordion(
        AccordionGroup("group name", "form_field_1", "form_field_2"),
        AccordionGroup("another group name", "form_field"),
        flush=True,
        always_open=True
    )
)
```

Support is added for [Switches](https://getbootstrap.com/docs/5.2/forms/checks-radios/#switches). Switches are a custom 
checkbox rendered as a toggle switch. The widget for these fields should be
a [CheckboxInput](https://docs.djangoproject.com/en/4.2/ref/forms/widgets/#django.forms.CheckboxInput).

```python
from crispy_bootstrap5.bootstrap5 import Switch

... Layout(Switch("is_company"))
```


## Development

To contribute to this library, first checkout the code. Then create a new virtual environment:

```bash
cd crispy-bootstrap5
python -m venv venv
source venv/bin/activate
```

Or if you are using `pipenv`:
```bash
pipenv shell
```

Now install the dependencies and tests:
```bash
pip install -e '.[test]'
```

To run the tests:
```bash
pytest
```

{% extends "partials/base.html" %}
{% load crispy_forms_tags %}
{% load static %}
{% load permission_tags %}

{% block content %}

<!-- Bootstrap CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

<style>
    /* Modern Bootstrap-Enhanced Wholesale Stock Check Styling */
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .main-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin: 20px auto;
        max-width: 1400px;
    }

    .header-section {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
        color: #2d3748;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(255, 154, 158, 0.3);
        margin-bottom: 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .header-section h2 {
        margin: 0;
        font-weight: 700;
        font-size: 1.8rem;
        display: flex;
        align-items: center;
    }

    .header-section h2 i {
        margin-right: 10px;
        font-size: 1.5rem;
        color: #667eea;
    }

    .btn-back {
        background: rgba(255, 255, 255, 0.3) !important;
        color: #2d3748 !important;
        border: 2px solid rgba(255, 255, 255, 0.5) !important;
        border-radius: 10px !important;
        padding: 10px 20px !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
        text-decoration: none !important;
    }

    .btn-back:hover {
        background: rgba(255, 255, 255, 0.5) !important;
        border-color: rgba(255, 255, 255, 0.7) !important;
        transform: translateY(-2px) !important;
        color: #2d3748 !important;
    }

    .info-card {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-left: 5px solid #ff9a9e;
    }

    .controls-section {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        backdrop-filter: blur(5px);
    }

    .btn-gradient-primary {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        border: none;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-gradient-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 154, 158, 0.4);
        color: white;
    }

    .main-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        border: none;
    }

    .card-header {
        background: linear-gradient(135deg, #4a5568, #2d3748) !important;
        color: white !important;
        border: none !important;
        padding: 20px !important;
    }

    .card-header h5 {
        margin: 0;
        font-weight: 600;
        font-size: 1.2rem;
        display: flex;
        align-items: center;
    }

    .card-header h5 i {
        margin-right: 10px;
        color: #ff9a9e;
    }

    .controls-section {
        background: rgba(255, 255, 255, 0.8);
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .form-check {
        background: rgba(255, 154, 158, 0.1);
        padding: 15px;
        border-radius: 10px;
        border: 2px solid rgba(255, 154, 158, 0.2);
        transition: all 0.3s ease;
    }

    .form-check:hover {
        background: rgba(255, 154, 158, 0.15);
        border-color: rgba(255, 154, 158, 0.3);
    }

    .form-check-input {
        width: 18px;
        height: 18px;
        accent-color: #ff9a9e;
        margin-right: 10px;
    }

    .form-check-label {
        font-weight: 600;
        color: #2d3748;
        cursor: pointer;
    }

    .form-control {
        border: 2px solid #e2e8f0 !important;
        border-radius: 12px !important;
        padding: 12px 20px !important;
        font-size: 1rem !important;
        transition: all 0.3s ease !important;
        background: white !important;
    }

    .form-control:focus {
        border-color: #ff9a9e !important;
        box-shadow: 0 0 0 3px rgba(255, 154, 158, 0.1) !important;
        outline: none !important;
        background: #f7fafc !important;
    }

    .table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border-collapse: separate;
        border-spacing: 0;
        margin: 0;
    }

    .table-dark th {
        background: linear-gradient(135deg, #4a5568, #2d3748) !important;
        color: white !important;
        font-weight: 600 !important;
        padding: 15px 12px !important;
        text-align: center !important;
        border: none !important;
        font-size: 0.9rem !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }

    .table tbody tr {
        transition: all 0.3s ease;
        border-bottom: 1px solid #e2e8f0;
    }

    .table tbody tr:hover {
        background: linear-gradient(135deg, #f7fafc, #edf2f7);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .table tbody td {
        padding: 15px 12px;
        vertical-align: middle;
        border: none;
        text-align: center;
    }

    .table tbody td:nth-child(2) {
        text-align: left;
        font-weight: 600;
        color: #2d3748;
    }

    /* Button Styling */
    .btn-primary {
        background: linear-gradient(135deg, #48bb78, #38a169) !important;
        color: white !important;
        border: none !important;
        border-radius: 12px !important;
        padding: 12px 30px !important;
        font-weight: 600 !important;
        box-shadow: 0 4px 15px rgba(56, 161, 105, 0.3) !important;
        transition: all 0.3s ease !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }

    .btn-primary:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(56, 161, 105, 0.4) !important;
        background: linear-gradient(135deg, #38a169, #2f855a) !important;
    }

    .btn-secondary {
        background: linear-gradient(135deg, #a0aec0, #718096) !important;
        color: white !important;
        border: none !important;
        border-radius: 12px !important;
        padding: 12px 30px !important;
        font-weight: 600 !important;
        box-shadow: 0 4px 15px rgba(113, 128, 150, 0.3) !important;
        transition: all 0.3s ease !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }

    .btn-secondary:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 6px 20px rgba(113, 128, 150, 0.4) !important;
        background: linear-gradient(135deg, #718096, #4a5568) !important;
    }

    .btn-outline-primary {
        border: 2px solid #ff9a9e !important;
        color: #ff9a9e !important;
        background: transparent !important;
        border-radius: 8px !important;
        padding: 8px 16px !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
    }

    .btn-outline-primary:hover {
        background: #ff9a9e !important;
        color: white !important;
        transform: translateY(-1px) !important;
    }

    .btn-outline-secondary {
        border: 2px solid #a0aec0 !important;
        color: #a0aec0 !important;
        background: transparent !important;
        border-radius: 8px !important;
        padding: 8px 16px !important;
        font-weight: 600 !important;
        transition: all 0.3s ease !important;
    }

    .btn-outline-secondary:hover {
        background: #a0aec0 !important;
        color: white !important;
        transform: translateY(-1px) !important;
    }

    /* Empty state styling */
    .empty-state {
        background: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 40px;
        text-align: center;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .empty-state i {
        color: #48bb78;
        margin-bottom: 20px;
    }

    .empty-state h5 {
        color: #4a5568;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .empty-state p {
        color: #718096;
        margin-bottom: 20px;
    }

    /* Enhanced form elements */
    input[type="checkbox"] {
        width: 18px;
        height: 18px;
        accent-color: #ff9a9e;
        cursor: pointer;
        transform: scale(1.2);
    }

    /* Price and stock styling */
    .price-cell {
        color: #38a169;
        font-weight: 600;
    }

    .stock-high {
        color: #38a169;
        font-weight: 600;
    }

    .stock-medium {
        color: #d69e2e;
        font-weight: 600;
    }

    .stock-low {
        color: #e53e3e;
        font-weight: 600;
    }

    @media (max-width: 768px) {
        .wholesale-stock-container {
            margin: 10px;
            padding: 20px;
            border-radius: 15px;
        }

        .header-section {
            flex-direction: column;
            gap: 15px;
            text-align: center;
        }

        .header-section h2 {
            font-size: 1.5rem;
        }

        .controls-section {
            padding: 15px;
        }

        .btn-primary, .btn-secondary {
            padding: 10px 20px !important;
            font-size: 0.9rem !important;
        }
    }
</style>

<div class="container-fluid">
    <div class="main-container p-4">
        <div class="header-section text-dark p-4 mb-4 rounded">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Add Items to Wholesale Stock Check #{{ stock_check.id }}</h2>
                </div>
                <div class="col-md-4 text-md-end">
                    <a href="{% url 'wholesale:update_wholesale_stock_check' stock_check.id %}" class="btn btn-back">
                        <i class="fas fa-arrow-left me-2"></i> Back to Stock Check
                    </a>
                </div>
            </div>
        </div>

        <!-- Stock Check Info -->
        <div class="info-card p-4 mb-4 rounded">
            <h5 class="mb-3"><i class="fas fa-info-circle me-2"></i> Wholesale Stock Check Information</h5>
            <div class="row">
                <div class="col-md-3 mb-2">
                    <strong><i class="fas fa-building me-1"></i> Stock Check ID:</strong> #{{ stock_check.id }}
                </div>
                <div class="col-md-3 mb-2">
                    <strong><i class="fas fa-user me-1"></i> Created By:</strong> {{ stock_check.created_by.get_full_name|default:stock_check.created_by.username }}
                </div>
                <div class="col-md-3 mb-2">
                    <strong><i class="fas fa-calendar me-1"></i> Date:</strong> {{ stock_check.date|date:"M d, Y H:i" }}
                </div>
                <div class="col-md-3 mb-2">
                    <strong><i class="fas fa-sync-alt me-1"></i> Status:</strong>
                    <span class="badge bg-warning text-dark">{{ stock_check.get_status_display }}</span>
                </div>
            </div>
        </div>

        <!-- Add Items Form -->
        <div class="card">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i> Available Items to Add</h5>
            </div>
            <div class="card-body">
            {% if available_items %}
            <form method="POST">
                {% csrf_token %}
                
                <!-- Options -->
                <div class="controls-section">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="zero_empty_items" value="true" id="zeroEmptyItems" checked>
                                <label class="form-check-label" for="zeroEmptyItems">
                                    📦 Include items with zero stock
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <input type="text" id="searchItems" class="form-control" placeholder="🔍 Search wholesale items...">
                            </div>
                        </div>
                    </div>

                    <!-- Select All -->
                    <div class="mb-3">
                        <button type="button" class="btn btn-sm btn-outline-primary" id="selectAll">
                            <i class="fas fa-check-square"></i> Select All
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-secondary ml-2" id="deselectAll">
                            <i class="fas fa-square"></i> Deselect All
                        </button>
                    </div>
                </div>

                <!-- Items Table -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="itemsTable">
                        <thead class="table-dark">
                            <tr>
                                <th><input type="checkbox" id="selectAllCheckbox"></th>
                                <th>Item Name</th>
                                <th>Dosage Form</th>
                                <th>Brand</th>
                                <th>Unit</th>
                                <th>Current Stock</th>
                                {% if user|can_view_financial_data %}
                                <th>Cost</th>
                                {% endif %}
                                <th>Price</th>
                                <th>Expiry Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in available_items %}
                            <tr class="item-row" data-item-name="{{ item.name|lower }}" data-brand="{{ item.brand|lower }}">
                                <td>
                                    <input type="checkbox" name="selected_items" value="{{ item.id }}" class="item-checkbox">
                                </td>
                                <td><strong>{{ item.name }}</strong></td>
                                <td>{{ item.dosage_form|default:"N/A" }}</td>
                                <td>{{ item.brand|default:"N/A" }}</td>
                                <td>{{ item.unit }}</td>
                                <td>
                                    <span class="badge {% if item.stock > 0 %}badge-success{% else %}badge-danger{% endif %}">
                                        {{ item.stock }}
                                    </span>
                                </td>
                                {% if user|can_view_financial_data %}
                                <td>₦{{ item.cost|floatformat:2 }}</td>
                                {% endif %}
                                <td>₦{{ item.price|floatformat:2 }}</td>
                                <td>
                                    {% if item.exp_date %}
                                        {{ item.exp_date|date:"M d, Y" }}
                                    {% else %}
                                        <span class="text-muted">N/A</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                    <!-- Submit Button -->
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-gradient-primary btn-lg px-5 me-3">
                            <i class="fas fa-plus me-2"></i> Add Selected Items to Stock Check
                        </button>
                        <a href="{% url 'wholesale:update_wholesale_stock_check' stock_check.id %}" class="btn btn-secondary btn-lg px-5">
                            <i class="fas fa-times me-2"></i> Cancel
                        </a>
                    </div>
                </form>
                {% else %}
                <div class="empty-state text-center p-5">
                    <i class="fas fa-check-circle fa-3x mb-3 text-success"></i>
                    <h5>All Items Already Added</h5>
                    <p class="text-muted">All available wholesale items are already included in this stock check.</p>
                    <a href="{% url 'wholesale:update_wholesale_stock_check' stock_check.id %}" class="btn btn-gradient-primary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Stock Check
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('searchItems');
    const itemRows = document.querySelectorAll('.item-row');
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        itemRows.forEach(row => {
            const itemName = row.dataset.itemName;
            const brand = row.dataset.brand;
            
            if (itemName.includes(searchTerm) || brand.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });
    
    // Select all functionality
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const itemCheckboxes = document.querySelectorAll('.item-checkbox');
    const selectAllBtn = document.getElementById('selectAll');
    const deselectAllBtn = document.getElementById('deselectAll');
    
    selectAllCheckbox.addEventListener('change', function() {
        itemCheckboxes.forEach(checkbox => {
            if (checkbox.closest('tr').style.display !== 'none') {
                checkbox.checked = this.checked;
            }
        });
    });
    
    selectAllBtn.addEventListener('click', function() {
        itemCheckboxes.forEach(checkbox => {
            if (checkbox.closest('tr').style.display !== 'none') {
                checkbox.checked = true;
            }
        });
        selectAllCheckbox.checked = true;
    });
    
    deselectAllBtn.addEventListener('click', function() {
        itemCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        selectAllCheckbox.checked = false;
    });
});
</script>

<!-- Bootstrap JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}
